using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common.Cache;
using YseStore.IService.Visual;

namespace YseStore.Service.Visual
{
    /// <summary>
    ///
    /// </summary>
    public class VisualTemplateServicee : BaseServices<visual_template>, IVisualTemplateServicee
    {
        private readonly ISqlSugarClient db;
        private readonly ICaching _caching;
        private readonly ILogger<VisualTemplateServicee> _logger;

        public VisualTemplateServicee(ISqlSugarClient db, ICaching caching, ILogger<VisualTemplateServicee> logger)
        {
            this.db = db;
            _caching = caching;
            _logger = logger;
        }

        /// <summary>
        /// 根据草稿ID和页面类型获取模板
        /// </summary>
        /// <param name="draftsId">草稿ID</param>
        /// <param name="pages">页面类型</param>
        /// <returns></returns>
        public async Task<visual_template> GetTemplateByDraftsIdAndPagesAsync(int draftsId, string pages)
        {
            try
            {
                return await db.Queryable<visual_template>()
                    .Where(it => it.DraftsId == draftsId && it.Pages == pages)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据草稿ID和页面类型获取模板失败，DraftsId: {DraftsId}, Pages: {Pages}", draftsId, pages);
                return null;
            }
        }
    }
}
