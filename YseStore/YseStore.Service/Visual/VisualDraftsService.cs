using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common.Cache;
using YseStore.IService.Visual;

namespace YseStore.Service.Visual
{
    /// <summary>
    /// 店铺装修草稿
    /// </summary>
    public class VisualDraftsService : BaseServices<visual_drafts>, IVisualDraftsService
    {
        private readonly ISqlSugarClient db;
        private readonly ICaching _caching;
        private readonly ILogger<VisualDraftsService> _logger;

        public VisualDraftsService(ISqlSugarClient db, ICaching caching, ILogger<VisualDraftsService> logger)
        {
            this.db = db;
            _caching = caching;
            _logger = logger;
        }

        /// <summary>
        /// 获取最新的可见草稿
        /// </summary>
        /// <returns></returns>
        public async Task<visual_drafts> GetLatestVisibleDraftAsync()
        {
            try
            {
                return await db.Queryable<visual_drafts>()
                    .Where(it => it.Visible == true)
                    .OrderByDescending(it => it.AccTime)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取最新可见草稿失败");
                return null;
            }
        }
    }
}
