<div class="page-header">
    <div class="page-title"><h1>{{ "user.account.settingTitle"|translate}}</h1></div>
</div>
<div class="container">
    <div class="row mb-5">
        {% assign sidebar= '/Themes/'| append: theme | append:'/Account/Sidebar' %}
        {% include sidebar -%}

        <div class="col-xs-10 col-lg-10 col-md-12">
            <!-- Tab panes -->
            <div class="dashboard-content padding-30px-all md-padding-15px-all" style="">
                <!-- Account Details -->
                    <h3>{{ "user.account.MyProfile_Information"|translate}}</h3>
                    <div class="account-login-form bg-light-gray padding-20px-all">
                        <form action="#" >
                         <fieldset>
                                <div class="row">
                                    <div class="form-group col-md-6 col-lg-6 col-xl-6 required mb-3">
                                        <label for="input-name">{{ "user.account.firstname"|translate}} <span class="required-f">*</span></label>
                                        <input name="FirstName" value="{{ Model.UserData.FirstName }}" type="text">
                                    </div>
                                    <div class="form-group col-md-6 col-lg-6 col-xl-6 required mb-3">
                                        <label for="input-lastname">{{ "user.account.lastname"|translate}} <span class="required-f">*</span></label>
                                        <input name="LastName" value="{{ Model.UserData.LastName }}" type="text">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group col-md-6 col-lg-6 col-xl-6 required mb-3">
                                        <label for="input-email">{{ "user.account.email_addr"|translate}} <span class="required-f">*</span></label>
                                        <input name="Email" value="{{ Model.UserData.Email }}" id="input-email" type="email">
                                    </div>
                                    <div class="form-group col-md-6 col-lg-6 col-xl-6 required mb-3">
                                        <label for="input-lastname">{{ "user.account.nickName"|translate}} <span class="required-f">*</span></label>
                                        <input name="NickName" value="{{ Model.UserData.NickName }}" type="text">
                                    </div>
                                </div>
                            </fieldset>
                            <button type="submit" id="SaveUser" class="btn margin-15px-top btn-primary theme-btn">{{ "web.global.save"|translate}}</button>
                        </form>
                    
                    </div>

                <!-- End Account Details -->
            </div>
            <!-- End Tab panes -->
        </div>
    </div>
</div>
<!-- 引入自定义弹窗JS库 -->
<script src="/js/Pop-ups/frame-message.js"></script>
<script>

// 原有代码保持不变，在文档底部添加以下内容
document.addEventListener('DOMContentLoaded', function() {
        

   $('#SaveUser').click(function(e){
        e.preventDefault();
        var data = {
            FirstName: $('input[name="FirstName"]').val(),
            LastName: $('input[name="LastName"]').val(),
            Email: $('input[name="Email"]').val(),
            NickName: $('input[name="NickName"]').val()
        };
            $.ajax({
                url: '/api/account/comment/SaveUser',
                type: 'POST',
                data: data,
                success: function(response) {
                    if(response.status) {
                       customize_pop.success('{{'web.global.submit_success'|translate}}');
                    } else {
                       customize_pop.error(response.msg);
                    }
                },
                error: function(xhr) {
                    console.error('AJAX Error:', xhr.msg);
                    console.log('提交失败：' + (xhr.statusText || '网络错误'));
                }
            });
    });
   



});



</script>


