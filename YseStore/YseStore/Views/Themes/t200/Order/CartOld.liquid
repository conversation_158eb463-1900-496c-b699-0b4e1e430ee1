<!--<PERSON> Header-->
<div class="page-header">
    <div class="page-title"><h1>Cart</h1></div>
</div>
<!--End Page Header-->
<div class="container">

    <div class="row">
        <!--Main Content-->
        {% if Model != null %}
        <div class="col-12 col-sm-12 col-md-12 col-lg-12 main-col">
            <!--<div class="alert alert-success text-uppercase" role="alert">
                <i class="icon an an-truck an-lg icon-large"></i> &nbsp;{{ "user.account.freeShipping"|translate}} !
            </div>-->
            <form action="#" method="post" class="cart style2">
                <div class="table-responsive">
                    <table>
                        <thead class="cart__row cart__header">
                            <tr>
                                <th></th>
                                <th class="text-center">{{ "web.global.image"|translate }}</th>
                                <th class="text-center">{{ "user.global.productName"|translate }}</th>
                                <th class="text-center">{{ "web.global.price"|translate }}</th>
                                <th class="text-center">{{ "web.global.qty"|translate }}</th>
                                <th class="text-center">{{ "web.global.total"|translate }}</th>
                                <th class="action">&nbsp;</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in Model.list %}

                            <tr class="cart__row border-bottom line1 cart-flex border-top">
                                <td>
                                    <input type="checkbox" class="form-check-box" value="1" {% if product.isInvalid %} disabled{% endif %} {% if product.isSelect %} checked {% endif %} />
                                </td>
                                <td class="cart__image-wrapper cart-flex-item">
                                    <a href="/product/{{product.products.PageUrl}}"><img class="blur-up lazyload cart__image" src="{{static_path}}{{product.PicPath}}" data-src="{{static_path}}{{product.PicPath}}" alt=""></a>
                                </td>
                                <td class="cart__meta small--text-left cart-flex-item">
                                    <div class="list-view-item__title">
                                        <a href="/product/{{product.products.PageUrl}}">{{product.products.Name_en}} </a>
                                    </div>

                                    <div class="cart__meta-text">

                                        {% if product.isMultiprop %}
                                        {% for prop in product.multiprop %}
                                        {{prop[0]}}:{{prop[1]}}<br>
                                        {% endfor %}

                                        {% endif %}
                                    </div>
                                </td>
                                <td class="cart__price-wrapper cart-flex-item text-center">
                                    <span class="money">{{product.priceFormat}}</span>
                                </td>
                                <td class="cart__update-wrapper cart-flex-item text-center">
                                    <div class="cart__qty text-center">
                                        <div class="qtyField">
                                            <a class="qtyBtn minus" href="javascript:void(0);"><i class="an an-minus"></i></a>
                                            <input class="cart__qty-input qty"
                                                    hx-trigger="change delay:500ms"  
                                                    hx-post="/cart/SetCartNum"
                                                    hx-vals='js:{"cId": "{{product.CId}}","nums": document.getElementById("nums{{product.CId}}").value }'
                                                    hx-on::after-request="if(event.detail.successful){
                                                      showToast(event.detail.xhr.responseText)
                                                     }"
                                                     hx-swap="none"
                                                   id="nums{{product.CId}}"
                                                   type="text" name="num"  value="{{product.nums}}" pattern="[0-9]*">
                                            <a class="qtyBtn plus" href="javascript:void(0);"><i class="an an-plus"></i></a>
                                        </div>
                                    </div>
                                </td>
                                <td class="small--hide cart-price text-center">
                                    <div><span class="money">{{product.itemTotalFormat}}</span></div>
                                </td>
                                <td class="text-center">
                                    <button hx-post="/cart/DeleteCart"
                                            hx-confirm="{{"web.global.del_confirm"|translate}}"
                                            hx-vals='{"cId": "{{product.CId}}"}'
                                            hx-on::after-request="if(event.detail.successful){
                                             showToast(event.detail.xhr.responseText)
                                            }"
                                            hx-swap="none"
                                            class="cart__remove" title="Remove item">
                                        <i class="icon icon an an-times"></i>
                                    </button>
                                </td>
                            </tr>

                            {% endfor %}
                            <tr>
                                <td colspan="4">{{"checkout.checkout.weight"|translate}}: {{Model.weightFormat}}</td>
                                <td>{{"checkout.checkout.grandTotal"|translate}}:</td>
                                <td>
                                    {% if Model.goodsAmount>Model.amount %}
                                    <del> {{Model.goodsAmountFormat}}</del>
                                    {% endif %}
                                </td>
                                <td>{{Model.amountFormat}}</td>
                            </tr>
                        </tbody>
                        <tfoot>

                            <tr>
                                <td colspan="3" class="text-left"><a href="/shop" class="btn cart-continue theme-btn"> <i class="an an-lg an-arrow-left "></i> {{ "user.forgot.continueShopping"|translate}}</a></td>
                                <td colspan="4" class="text-right">
                                    <button type="submit"
                                            hx-post="/cart/DoDeleteAll"
                                            hx-confirm="{{"web.global.del_confirm"|translate}}"
                                            hx-on::after-request="if(event.detail.successful){
                                            showToast(event.detail.xhr.responseText)
                                            }"
                                            hx-swap="none"
                                            name="clear" class="btn small--hide theme-btn">
                                        <i class="an an-lg an-times"></i> {{ "web.global.clear"|translate }}
                                    </button>
                                    <button type="submit" name="update" class="btn small--hide cart-continue ms-2 theme-btn">
                                        <i class="an an-lg an-sync-alt">
                                        </i> {{ "web.global.update"|translate }} {{ "web.header.cartStr"|translate}}
                                    </button>
                                    <button type="submit" name="checkout" class="btn cart-check-out ms-2 theme-btn">{{ "cart.global.checkOut"|translate }}  <i class="an an-lg an-arrow-right"></i> </button>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </form>
        </div>
        {% else %}
        <div class="empty-page-content text-center">
            <h1 class="mb-4">{{"cart.global.empty"|translate}}</h1>
            <p><a href="/shop" class="btn btn--has-icon-after">{{"user.forgot.continueShopping"|translate}} <i class="fa fa-caret-right" aria-hidden="true"></i></a></p>
        </div>
        {% endif %}
        <!--End Main Content-->
    </div>


</div>

<style>
    .empty-page-content {
        padding: 140px 0;
    }
</style>
<!-- 现代化消息提示框 -->
<script src="/js/Pop-ups/frame-message.js"></script>
<script src="/assets/js/cart.js"></script>

<script>


    function showToast(responseText) {
        const response = JSON.parse(responseText);
        if (response.status == true) {
            customize_pop.success(response.msg, function () {

            }, null, { showIcon: true });
        } else {
            customize_pop.error(response.msg, null, null, { showIcon: true });
        }
    }
</script>