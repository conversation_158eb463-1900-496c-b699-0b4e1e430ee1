using Entitys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.IService.Visual
{
    /// <summary>
    ///
    /// </summary>
    public interface IVisualTemplateServicee : IBaseServices<visual_template>
    {
        /// <summary>
        /// 根据草稿ID和页面类型获取模板
        /// </summary>
        /// <param name="draftsId">草稿ID</param>
        /// <param name="pages">页面类型</param>
        /// <returns></returns>
        Task<visual_template> GetTemplateByDraftsIdAndPagesAsync(int draftsId, string pages);
    }
}
