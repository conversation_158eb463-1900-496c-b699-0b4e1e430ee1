using Entitys;


namespace YseStore.IService.Visual
{
    public interface IVisualPagesService : IBaseServices<visual_pages>
    {
        Task<visual_pages> QueryVisualPagesAsync(int pagesId, int draftsId, string pages);

        /// <summary>
        /// 根据模板ID获取页面配置
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns></returns>
        Task<visual_pages> GetPagesByTemplateIdAsync(int templateId);
    }
}
